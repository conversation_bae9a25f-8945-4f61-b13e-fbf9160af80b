//! 独立的Raft日志存储实现
//! 
//! 这个模块实现了openraft 0.9需要的RaftLogStorage trait，
//! 将日志管理与状态机逻辑分离。

use crate::raft::types::*;
use crate::raft::store::Store;
use openraft::{
    storage::{LogState, RaftLogStorage},
    Entry, LogId, OptionalSend, StorageError, Vote,
};
use std::ops::RangeBounds;
use std::sync::Arc;
use tracing::{debug, error, info};

/// 独立的Raft日志存储实现
/// 
/// 这个实现专注于日志管理，与状态机逻辑完全分离
#[derive(Debug, Clone)]
pub struct ConfluxLogStorage {
    store: Arc<Store>,
}

impl ConfluxLogStorage {
    /// 创建新的日志存储实例
    pub fn new(store: Arc<Store>) -> Self {
        Self { store }
    }
}

impl RaftLogStorage<TypeConfig> for ConfluxLogStorage {
    async fn get_log_state(&mut self) -> Result<LogState<TypeConfig>, StorageError<NodeId>> {
        debug!("Getting log state");
        
        let logs = self.store.logs.read().await;
        let last_log_id = if let Some((_, last_entry_json)) = logs.iter().next_back() {
            let entry: Entry<TypeConfig> = serde_json::from_str(last_entry_json)
                .map_err(|e| StorageError::IO {
                    source: openraft::StorageIOError::new(
                        openraft::ErrorSubject::Logs,
                        openraft::ErrorVerb::Read,
                        openraft::AnyError::error(format!("Failed to deserialize log entry: {}", e)),
                    ),
                })?;
            Some(entry.log_id)
        } else {
            None
        };

        let last_purged = *self.store.last_purged_log_id.read().await;

        Ok(LogState {
            last_purged_log_id: last_purged,
            last_log_id,
        })
    }

    async fn save_vote(&mut self, vote: &Vote<NodeId>) -> Result<(), StorageError<NodeId>> {
        debug!("Saving vote: {:?}", vote);
        
        let mut stored_vote = self.store.vote.write().await;
        *stored_vote = Some(*vote);
        
        info!("Vote saved successfully");
        Ok(())
    }

    async fn read_vote(&mut self) -> Result<Option<Vote<NodeId>>, StorageError<NodeId>> {
        debug!("Reading vote");
        
        let vote = *self.store.vote.read().await;
        
        debug!("Retrieved vote: {:?}", vote);
        Ok(vote)
    }

    async fn append<I>(&mut self, entries: I) -> Result<(), StorageError<NodeId>>
    where
        I: IntoIterator<Item = Entry<TypeConfig>> + OptionalSend,
    {
        debug!("Appending entries to log");
        
        let mut logs = self.store.logs.write().await;
        let mut count = 0;
        
        for entry in entries {
            let serialized = serde_json::to_string(&entry)
                .map_err(|e| StorageError::IO {
                    source: openraft::StorageIOError::new(
                        openraft::ErrorSubject::Logs,
                        openraft::ErrorVerb::Write,
                        openraft::AnyError::error(format!("Failed to serialize log entry: {}", e)),
                    ),
                })?;
            
            logs.insert(entry.log_id.index, serialized);
            count += 1;
        }
        
        info!("Appended {} entries to log", count);
        Ok(())
    }

    async fn truncate(&mut self, log_id: LogId<NodeId>) -> Result<(), StorageError<NodeId>> {
        debug!("Truncating logs since: {:?}", log_id);
        
        let mut logs = self.store.logs.write().await;
        let keys_to_remove: Vec<u64> = logs
            .range(log_id.index..)
            .map(|(k, _)| *k)
            .collect();
        
        for key in &keys_to_remove {
            logs.remove(key);
        }
        
        info!("Truncated {} logs since index {}", keys_to_remove.len(), log_id.index);
        Ok(())
    }

    async fn purge(&mut self, log_id: LogId<NodeId>) -> Result<(), StorageError<NodeId>> {
        debug!("Purging logs up to: {:?}", log_id);
        
        // Update last purged log ID
        {
            let mut last_purged = self.store.last_purged_log_id.write().await;
            if let Some(current_purged) = *last_purged {
                if current_purged > log_id {
                    error!("Attempted to purge logs before already purged log ID");
                    return Err(StorageError::IO {
                        source: openraft::StorageIOError::new(
                            openraft::ErrorSubject::Logs,
                            openraft::ErrorVerb::Write,
                            openraft::AnyError::error("Cannot purge logs before already purged log ID"),
                        ),
                    });
                }
            }
            *last_purged = Some(log_id);
        }

        // Remove logs from storage
        {
            let mut logs = self.store.logs.write().await;
            let keys_to_remove: Vec<u64> = logs
                .range(..=log_id.index)
                .map(|(k, _)| *k)
                .collect();
            
            for key in &keys_to_remove {
                logs.remove(key);
            }
            
            info!("Purged {} logs up to index {}", keys_to_remove.len(), log_id.index);
        }
        
        Ok(())
    }

    type LogReader = ConfluxLogReader;

    async fn get_log_reader(&mut self) -> Self::LogReader {
        debug!("Creating log reader");
        ConfluxLogReader::new(self.store.clone())
    }
}

/// Raft日志读取器实现
#[derive(Debug, Clone)]
pub struct ConfluxLogReader {
    store: Arc<Store>,
}

impl ConfluxLogReader {
    /// 创建新的日志读取器
    pub fn new(store: Arc<Store>) -> Self {
        Self { store }
    }
}

impl openraft::storage::RaftLogReader<TypeConfig> for ConfluxLogReader {
    async fn try_get_log_entries<RB: RangeBounds<u64> + Clone + Send>(
        &mut self,
        range: RB,
    ) -> Result<Vec<Entry<TypeConfig>>, StorageError<NodeId>> {
        debug!("Reading log entries in range");
        
        let logs = self.store.logs.read().await;
        let mut entries = Vec::new();
        
        for (index, entry_json) in logs.range(range) {
            match serde_json::from_str::<Entry<TypeConfig>>(entry_json) {
                Ok(entry) => {
                    entries.push(entry);
                }
                Err(e) => {
                    error!("Failed to deserialize log entry at index {}: {}", index, e);
                    return Err(StorageError::IO {
                        source: openraft::StorageIOError::new(
                            openraft::ErrorSubject::Logs,
                            openraft::ErrorVerb::Read,
                            anyhow::anyhow!("Failed to deserialize log entry: {}", e).into(),
                        ),
                    });
                }
            }
        }
        
        debug!("Retrieved {} log entries", entries.len());
        Ok(entries)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::raft::store::Store;
    use openraft::{Entry, EntryPayload, LogId, CommittedLeaderId};
    use tempfile::TempDir;

    async fn create_test_log_storage() -> (ConfluxLogStorage, TempDir) {
        let temp_dir = tempfile::tempdir().unwrap();
        let store = Arc::new(Store::new(temp_dir.path().to_str().unwrap()).await.unwrap());
        let log_storage = ConfluxLogStorage::new(store);
        (log_storage, temp_dir)
    }

    #[tokio::test]
    async fn test_log_storage_creation() {
        let (log_storage, _temp_dir) = create_test_log_storage().await;
        assert!(log_storage.store.logs.read().await.is_empty());
    }

    #[tokio::test]
    async fn test_append_and_read_logs() {
        let (mut log_storage, _temp_dir) = create_test_log_storage().await;
        
        let entry = Entry {
            log_id: LogId::new(CommittedLeaderId::new(1, 1), 1),
            payload: EntryPayload::Blank,
        };

        // Append entry
        log_storage.append(vec![entry.clone()]).await.unwrap();
        
        // Read back
        let mut reader = log_storage.get_log_reader().await;
        let entries = reader.try_get_log_entries(1..=1).await.unwrap();
        
        assert_eq!(entries.len(), 1);
        assert_eq!(entries[0].log_id, entry.log_id);
    }

    #[tokio::test]
    async fn test_vote_storage() {
        let (mut log_storage, _temp_dir) = create_test_log_storage().await;
        
        let vote = Vote::new(1, 1);
        
        // Save vote
        log_storage.save_vote(&vote).await.unwrap();
        
        // Read vote
        let stored_vote = log_storage.read_vote().await.unwrap();
        assert_eq!(stored_vote, Some(vote));
    }

    #[tokio::test]
    async fn test_purge_logs() {
        let (mut log_storage, _temp_dir) = create_test_log_storage().await;
        
        // Append multiple entries
        let entries = vec![
            Entry {
                log_id: LogId::new(CommittedLeaderId::new(1, 1), 1),
                payload: EntryPayload::Blank,
            },
            Entry {
                log_id: LogId::new(CommittedLeaderId::new(1, 1), 2),
                payload: EntryPayload::Blank,
            },
            Entry {
                log_id: LogId::new(CommittedLeaderId::new(1, 1), 3),
                payload: EntryPayload::Blank,
            },
        ];
        
        log_storage.append(entries).await.unwrap();
        
        // Purge up to index 2
        let purge_id = LogId::new(CommittedLeaderId::new(1, 1), 2);
        log_storage.purge(purge_id).await.unwrap();
        
        // Check that only entry 3 remains
        let mut reader = log_storage.get_log_reader().await;
        let remaining_entries = reader.try_get_log_entries(..).await.unwrap();
        
        assert_eq!(remaining_entries.len(), 1);
        assert_eq!(remaining_entries[0].log_id.index, 3);
    }
}